import type { StableTmTransportDailyRecord, StableTmTransportInStatus, StableTmTransportOutHandoverNote, Staff } from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { client as prisma } from '@/repositories/client';
import { DatabaseError } from '@/repositories/database_error';
import type { RepositoryFunction as RF } from '@/repositories/repository';


export type HorseTransportRecord = PrismaTransportRecord & {
  horse: HorsesWithStableHistories;
  transportDailyRecord: StableTmTransportDailyRecord | null;
  transportInStatus:
    | (StableTmTransportInStatus & {
        staff: Staff | null;
      })
    | null;
  transportOutStatus:
    | (StableTmTransportOutStatus & {
        transportOutHandoverNotes: StableTmTransportOutHandoverNote[];
        farm: OutsideFarm | null;
        staff: Staff | null;
      })
    | null;
};


type FetchHorseTransportRecordsInput = {
  transportDailyRecordIds: Uint8Array[];
};

export const fetchHorseTransportRecords: RF<
  FetchHorseTransportRecordsInput,
  HorseTransportRecord[]
> = async (input, tx) => {
  const client = tx ?? prisma;
  if ('transportDailyRecordId' in input) {
    const records = await client.stableTmTransportRecord.findMany({
      where: {
        transportDailyRecordId: input.transportDailyRecordId,
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: 'desc',
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
      orderBy: {
        index: 'asc',
      },
    });
    return records;
  }
  if ('transportDailyRecordIds' in input) {
    const { transportDailyRecordIds } = input;
    const records = await client.stableTmTransportRecord.findMany({
      where: {
        transportDailyRecordId: { in: transportDailyRecordIds },
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: 'desc',
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
      orderBy: [
        {
          transportDailyRecordId: 'asc',
        },
        {
          index: 'asc',
        },
      ],
    });
    return records;
  }
  throw new ConnectError('Invalid input', Code.InvalidArgument);
};

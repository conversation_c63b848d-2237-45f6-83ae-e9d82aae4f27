import type { StableTmTransportDailyRecord } from '@prisma/client';
import { ResultAsync } from 'neverthrow';
import { client as prisma } from '@/repositories/client';
import { DatabaseError } from '@/repositories/database_error';
import type { RepositoryFunction as RF } from '@/repositories/repository';

export const getUnconfirmedRecordsBeforeDate: RF<{ date: Date }, StableTmTransportDailyRecord[]> = (
  { date },
  tx,
) => {
  const client = tx || prisma;
  const targetYear = date.getFullYear();
  const targetMonth = date.getMonth() + 1; // JavaScript months are 0-indexed
  const targetDay = date.getDate();

  const result = ResultAsync.fromPromise(
    client.stableTmTransportDailyRecord.findMany({
      where: {
        isConfirmed: false,
        OR: [
          {
            year: {
              lt: targetYear,
            },
          },
          {
            year: targetYear,
            month: {
              lt: targetMonth,
            },
          },
          {
            year: targetYear,
            month: targetMonth,
            day: {
              lt: targetDay,
            },
          },
        ],
      },
      orderBy: [{ year: 'asc' }, { month: 'asc' }, { day: 'asc' }],
    }),
    error => {
      const message = error instanceof Error ? error.message : 'Unknown database error';
      return new DatabaseError(`Failed to fetch unconfirmed records: ${message}`);
    },
  );
  return result;
};
